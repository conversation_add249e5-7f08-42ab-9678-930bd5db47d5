import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types for Analytics Data
export interface KPICard {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  period: string;
  color: 'green' | 'blue' | 'orange' | 'purple' | 'red';
}

export interface ChannelData {
  name: string;
  value: number;
  percentage: number;
  growth: number;
}

export interface CustomerSegment {
  name: string;
  count: number;
  revenue: number;
  clv: number;
  growth: number;
}

export interface Campaign {
  id: string;
  name: string;
  spend: number;
  revenue: number;
  roas: number;
  conversions: number;
  status: 'active' | 'paused' | 'completed';
  startDate: string;
  endDate?: string;
}

export interface ProductPerformanceData {
  id: number;
  name: string;
  category: string;
  revenue: number;
  units: number;
  margin: number;
  growth: number;
  trend: 'up' | 'down' | 'neutral';
}

export interface SmartInsight {
  id: string;
  type: 'opportunity' | 'alert' | 'trend' | 'recommendation';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  recommendation: string;
  impact: string;
  confidence: number;
  createdAt: string;
}

export interface AnalyticsContextType {
  // KPI Data
  kpiCards: KPICard[];
  
  // Revenue Analytics
  revenueData: {
    total: number;
    growth: number;
    monthly: number[];
    channels: ChannelData[];
  };
  
  // Customer Analytics
  customerData: {
    segments: CustomerSegment[];
    totalCustomers: number;
    acquisitionCost: number;
    retentionRate: number;
  };
  
  // Campaign Analytics
  campaignData: {
    campaigns: Campaign[];
    totalSpend: number;
    averageROAS: number;
  };
  
  // Product Analytics
  productData: {
    topPerformers: ProductPerformanceData[];
    categories: { name: string; revenue: number; growth: number }[];
  };
  
  // Smart Insights
  insights: SmartInsight[];
  
  // Filters
  dateRange: { start: string; end: string };
  selectedChannels: string[];
  selectedSegments: string[];
  
  // Actions
  setDateRange: (range: { start: string; end: string }) => void;
  setSelectedChannels: (channels: string[]) => void;
  setSelectedSegments: (segments: string[]) => void;
  refreshData: () => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

// Sample Analytics Data
const sampleKPICards: KPICard[] = [
  {
    title: "Total Revenue",
    value: "$2.4M",
    change: "+12.5%",
    trend: "up",
    period: "vs last month",
    color: "green"
  },
  {
    title: "Conversion Rate",
    value: "3.2%",
    change: "+0.4%",
    trend: "up",
    period: "vs last month",
    color: "blue"
  },
  {
    title: "Average Order Value",
    value: "$89.50",
    change: "-2.1%",
    trend: "down",
    period: "vs last month",
    color: "orange"
  },
  {
    title: "Customer Acquisition Cost",
    value: "$28.40",
    change: "-8.3%",
    trend: "up",
    period: "vs last month",
    color: "green"
  },
  {
    title: "Return on Ad Spend",
    value: "4.2x",
    change: "+0.8x",
    trend: "up",
    period: "vs last month",
    color: "purple"
  }
];

const sampleRevenueData = {
  total: 2400000,
  growth: 12.5,
  monthly: [180000, 195000, 210000, 225000, 240000, 255000],
  channels: [
    { name: "Organic Search", value: 960000, percentage: 40, growth: 15.2 },
    { name: "Paid Search", value: 720000, percentage: 30, growth: 8.7 },
    { name: "Social Media", value: 480000, percentage: 20, growth: 22.1 },
    { name: "Email", value: 240000, percentage: 10, growth: 5.3 }
  ]
};

const sampleCustomerData = {
  segments: [
    { name: "VIP", count: 1200, revenue: 840000, clv: 700, growth: 18.5 },
    { name: "Regular", count: 8500, revenue: 1200000, clv: 141, growth: 12.3 },
    { name: "New", count: 3200, revenue: 320000, clv: 100, growth: 25.7 },
    { name: "At Risk", count: 800, revenue: 40000, clv: 50, growth: -15.2 }
  ],
  totalCustomers: 13700,
  acquisitionCost: 28.40,
  retentionRate: 78.5
};

const sampleCampaignData = {
  campaigns: [
    {
      id: "1",
      name: "Summer Sale 2024",
      spend: 45000,
      revenue: 189000,
      roas: 4.2,
      conversions: 1890,
      status: "active" as const,
      startDate: "2024-06-01"
    },
    {
      id: "2",
      name: "Back to School",
      spend: 32000,
      revenue: 128000,
      roas: 4.0,
      conversions: 1280,
      status: "completed" as const,
      startDate: "2024-08-01",
      endDate: "2024-08-31"
    }
  ],
  totalSpend: 77000,
  averageROAS: 4.1
};

const sampleProductData = {
  topPerformers: [
    { id: 1, name: "Premium Leather Tote", category: "Totes", revenue: 156000, units: 780, margin: 45, growth: 18.2, trend: "up" as const },
    { id: 2, name: "Designer Crossbody", category: "Crossbody", revenue: 132000, units: 880, margin: 52, growth: 15.7, trend: "up" as const },
    { id: 3, name: "Evening Clutch", category: "Clutches", revenue: 98000, units: 490, margin: 60, growth: -5.2, trend: "down" as const }
  ],
  categories: [
    { name: "Totes", revenue: 450000, growth: 12.5 },
    { name: "Crossbody", revenue: 380000, growth: 18.7 },
    { name: "Clutches", revenue: 290000, growth: -2.1 },
    { name: "Handbags", revenue: 520000, growth: 8.9 }
  ]
};

const sampleInsights: SmartInsight[] = [
  {
    id: "1",
    type: "opportunity",
    priority: "high",
    title: "Untapped Customer Segment",
    description: "25-34 age group shows 40% higher CLV but only represents 15% of current customers",
    recommendation: "Increase targeting for 25-34 demographic in paid campaigns",
    impact: "Potential +$180K revenue increase",
    confidence: 85,
    createdAt: "2024-01-15"
  },
  {
    id: "2",
    type: "alert",
    priority: "medium",
    title: "Declining Email Performance",
    description: "Email campaign CTR dropped 23% in last 30 days",
    recommendation: "A/B test subject lines and send times",
    impact: "Potential recovery of $45K monthly revenue",
    confidence: 72,
    createdAt: "2024-01-14"
  },
  {
    id: "3",
    type: "trend",
    priority: "low",
    title: "Seasonal Pattern Detected",
    description: "Crossbody bags show 35% sales increase every October",
    recommendation: "Increase inventory and marketing spend for crossbody category in September",
    impact: "Optimize inventory turnover",
    confidence: 91,
    createdAt: "2024-01-13"
  }
];

export const AnalyticsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [kpiCards] = useState<KPICard[]>(sampleKPICards);
  const [revenueData] = useState(sampleRevenueData);
  const [customerData] = useState(sampleCustomerData);
  const [campaignData] = useState(sampleCampaignData);
  const [productData] = useState(sampleProductData);
  const [insights] = useState<SmartInsight[]>(sampleInsights);
  
  const [dateRange, setDateRange] = useState({ start: "2024-01-01", end: "2024-01-31" });
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [selectedSegments, setSelectedSegments] = useState<string[]>([]);

  const refreshData = () => {
    // In a real app, this would fetch fresh data from the API
    console.log("Refreshing analytics data...");
  };

  const value: AnalyticsContextType = {
    kpiCards,
    revenueData,
    customerData,
    campaignData,
    productData,
    insights,
    dateRange,
    selectedChannels,
    selectedSegments,
    setDateRange,
    setSelectedChannels,
    setSelectedSegments,
    refreshData
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};

export const useAnalytics = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};
