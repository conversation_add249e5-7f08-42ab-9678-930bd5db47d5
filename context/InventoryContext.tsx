import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types
export interface Product {
  id: number;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  status: string;
  image: string;
  unitsSold?: number;
  revenue?: number;
  profitMargin?: number;
  daysToSell?: number;
  stockLevel?: string;
  daysInInventory?: number;
  salesVelocity?: number;
  suggestedAction?: string;
  suggestedDiscount?: string;
}

export interface InventoryContextType {
  products: Product[];
  bestSellers: Product[];
  slowMoving: Product[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  getProductById: (id: number) => Product | undefined;
  updateProduct: (id: number, updates: Partial<Product>) => void;
}

const InventoryContext = createContext<InventoryContextType | undefined>(undefined);

// Sample data - in a real app, this would come from an API
const sampleProducts: Product[] = [
  {
    id: 1,
    name: 'Classic Tote',
    sku: 'BAG-001-BLK',
    category: 'Tote Bags',
    price: 89.99,
    stock: 45,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20luxurious%20black%20leather%20tote%20bag%20with%20gold%20hardware%20details%20on%20a%20clean%20white%20background.%20The%20bag%20has%20a%20structured%20shape%20with%20a%20minimalist%20design%2C%20perfect%20for%20professional%20settings.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=1&orientation=squarish',
    unitsSold: 156,
    revenue: 14038.44,
    profitMargin: 45,
    daysToSell: 2.1,
    stockLevel: 'Optimal'
  },
  {
    id: 2,
    name: 'Evening Clutch',
    sku: 'BAG-002-RED',
    category: 'Clutches',
    price: 129.99,
    stock: 3,
    status: 'Low Stock',
    image: 'https://readdy.ai/api/search-image?query=An%20elegant%20red%20satin%20evening%20clutch%20with%20crystal%20embellishments%20on%20a%20clean%20white%20background.%20The%20clutch%20has%20a%20sleek%20rectangular%20shape%20with%20a%20gold%20chain%20strap.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=2&orientation=squarish',
    unitsSold: 95,
    revenue: 12349.05,
    profitMargin: 60,
    daysToSell: 3.5,
    stockLevel: 'Low'
  },
  {
    id: 3,
    name: 'Leather Handbag',
    sku: 'BAG-003-BRN',
    category: 'Handbags',
    price: 199.99,
    stock: 22,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20premium%20brown%20leather%20handbag%20with%20structured%20silhouette%20and%20gold%20hardware%20on%20a%20clean%20white%20background.%20The%20bag%20features%20multiple%20compartments%20and%20a%20detachable%20shoulder%20strap.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=3&orientation=squarish',
    unitsSold: 76,
    revenue: 15199.24,
    profitMargin: 47,
    daysToSell: 4.2,
    stockLevel: 'Optimal'
  },
  {
    id: 4,
    name: 'Canvas Tote',
    sku: 'BAG-004-NAT',
    category: 'Tote Bags',
    price: 59.99,
    stock: 67,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20casual%20beige%20canvas%20tote%20bag%20with%20leather%20trim%20details%20on%20a%20clean%20white%20background.%20The%20bag%20has%20a%20spacious%20interior%20with%20a%20simple%20minimalist%20design.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=4&orientation=squarish',
    unitsSold: 118,
    revenue: 7078.82,
    profitMargin: 48,
    daysToSell: 2.8,
    stockLevel: 'Optimal'
  },
  {
    id: 5,
    name: 'Crossbody Bag',
    sku: 'BAG-005-BLU',
    category: 'Crossbody',
    price: 79.99,
    stock: 18,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20navy%20blue%20crossbody%20bag%20with%20adjustable%20strap%20and%20silver%20hardware%20on%20a%20clean%20white%20background.%20The%20bag%20has%20a%20compact%20size%20with%20multiple%20pockets%20and%20a%20modern%20design.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=5&orientation=squarish',
    unitsSold: 132,
    revenue: 10558.68,
    profitMargin: 52,
    daysToSell: 2.5,
    stockLevel: 'Low'
  },
  {
    id: 6,
    name: 'Vintage Hobo Bag',
    sku: 'BAG-112-TAN',
    category: 'Hobo Bags',
    price: 149.99,
    stock: 28,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20tan%20leather%20hobo%20bag%20with%20vintage%20style%20and%20gold%20hardware%20on%20a%20clean%20white%20background.%20The%20bag%20has%20a%20slouchy%20silhouette%20with%20minimal%20design%20and%20a%20comfortable%20shoulder%20strap.%20High-quality%20product%20photography%20with%20soft%20shadows%20and%20excellent%20lighting&width=300&height=300&seq=101&orientation=squarish',
    unitsSold: 15,
    revenue: 2249.85,
    profitMargin: 35,
    daysToSell: 12.5,
    stockLevel: 'Optimal',
    daysInInventory: 187,
    salesVelocity: 0.15,
    suggestedAction: 'Discount',
    suggestedDiscount: '25%'
  },
  {
    id: 7,
    name: 'Structured Satchel',
    sku: 'BAG-089-GRY',
    category: 'Handbags',
    price: 179.99,
    stock: 17,
    status: 'In Stock',
    image: 'https://readdy.ai/api/search-image?query=A%20structured%20gray%20leather%20satchel%20handbag%20with%20silver%20hardware%20and%20clean%20lines%20on%20a%20white%20background.%20The%20bag%20features%20a%20rigid%20shape%20with%20multiple%20compartments%20and%20a%20detachable%20shoulder%20strap.%20Professional%20product%20photography%20with%20perfect%20lighting%20and%20subtle%20shadows&width=300&height=300&seq=102&orientation=squarish',
    unitsSold: 8,
    revenue: 1439.92,
    profitMargin: 40,
    daysToSell: 15.2,
    stockLevel: 'Low',
    daysInInventory: 162,
    salesVelocity: 0.21,
    suggestedAction: 'Bundle',
    suggestedDiscount: '15%'
  }
];

export const InventoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>(sampleProducts);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Derived data
  const bestSellers = products
    .filter(p => p.unitsSold && p.unitsSold > 100)
    .sort((a, b) => (b.unitsSold || 0) - (a.unitsSold || 0));

  const slowMoving = products
    .filter(p => p.daysInInventory && p.daysInInventory > 120)
    .sort((a, b) => (b.daysInInventory || 0) - (a.daysInInventory || 0));

  const getProductById = (id: number): Product | undefined => {
    return products.find(p => p.id === id);
  };

  const updateProduct = (id: number, updates: Partial<Product>) => {
    setProducts(prev => prev.map(p => p.id === id ? { ...p, ...updates } : p));
  };

  const value: InventoryContextType = {
    products,
    bestSellers,
    slowMoving,
    searchTerm,
    setSearchTerm,
    selectedCategory,
    setSelectedCategory,
    getProductById,
    updateProduct
  };

  return (
    <InventoryContext.Provider value={value}>
      {children}
    </InventoryContext.Provider>
  );
};

export const useInventory = (): InventoryContextType => {
  const context = useContext(InventoryContext);
  if (!context) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};
